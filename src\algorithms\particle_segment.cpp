#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <sstream>
#include "ImageUtils.h"
#include "ArgumentParser.h"

class ParticleSegmentation {
public:
    struct ParticleInfo {
        cv::Point2f center;
        double area;
        double diameter_px;
        double diameter_um;
        double circularity;
        bool is_valid;
        cv::Rect bounding_rect;
    };

    static double detectScaleBar(const cv::Mat& image, double scale_bar_real_length_um) {
        std::cout << "=== Scale bar detection started ===" << std::endl;

        cv::Mat gray, binary;
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY_INV | cv::THRESH_OTSU);

        // Horizontal morphological opening to detect scale bar
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(50, 3));
        cv::Mat scale_candidates;
        cv::morphologyEx(binary, scale_candidates, cv::MORPH_OPEN, kernel);

        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(scale_candidates, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        double max_width = 0;
        cv::Rect scale_rect;

        for (const auto& contour : contours) {
            cv::Rect rect = cv::boundingRect(contour);
            double aspect_ratio = static_cast<double>(rect.width) / rect.height;

            // Scale bar features: high aspect ratio, located at bottom of image
            if (aspect_ratio > 10 && rect.width > 80 && rect.y > image.rows * 0.7) {
                if (rect.width > max_width) {
                    max_width = rect.width;
                    scale_rect = rect;
                }
            }
        }

        if (max_width > 0) {
            double um_per_pixel = scale_bar_real_length_um / max_width;
            std::cout << "Scale bar detected successfully: " << max_width << " pixels = "
                      << scale_bar_real_length_um << " um" << std::endl;
            std::cout << "Conversion factor: " << std::fixed << std::setprecision(4)
                      << um_per_pixel << " um/pixel" << std::endl;
            return um_per_pixel;
        }

        std::cout << "Warning: Scale bar not detected, using pixel units" << std::endl;
        return 0.0;
    }

    static cv::Mat preprocessImage(const cv::Mat& src) {
        std::cout << "=== Image preprocessing started ===" << std::endl;

        cv::Mat gray, enhanced, blurred;
        cv::cvtColor(src, gray, cv::COLOR_BGR2GRAY);

        // Enhanced CLAHE for better small particle detection
        cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(3.0, cv::Size(6, 6));
        clahe->apply(gray, enhanced);

        // Reduced Gaussian blur to preserve small particle details
        cv::GaussianBlur(enhanced, blurred, cv::Size(3, 3), 0.8);

        std::cout << "Preprocessing completed: Enhanced CLAHE + reduced Gaussian blur" << std::endl;
        return blurred;
    }

    static cv::Mat createBinaryMask(const cv::Mat& preprocessed) {
        std::cout << "=== Binary processing ===" << std::endl;

        cv::Mat binary, cleaned;

        // Try both Otsu and adaptive thresholding for better small particle detection
        cv::Mat otsu_binary, adaptive_binary;
        cv::threshold(preprocessed, otsu_binary, 0, 255, cv::THRESH_BINARY_INV | cv::THRESH_OTSU);
        cv::adaptiveThreshold(preprocessed, adaptive_binary, 255, cv::ADAPTIVE_THRESH_GAUSSIAN_C,
                             cv::THRESH_BINARY_INV, 15, 2);

        // Combine both thresholding methods
        cv::bitwise_or(otsu_binary, adaptive_binary, binary);

        // Minimal morphological operations to preserve small particles
        cv::Mat kernel_open = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(1, 1));
        cv::Mat kernel_close = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));

        cv::morphologyEx(binary, cleaned, cv::MORPH_OPEN, kernel_open);
        cv::morphologyEx(cleaned, cleaned, cv::MORPH_CLOSE, kernel_close, cv::Point(-1,-1), 1);

        std::cout << "Binarization completed: Combined Otsu + adaptive threshold + minimal morphology" << std::endl;
        return cleaned;
    }

    static cv::Mat performWatershed(const cv::Mat& src, const cv::Mat& binary) {
        std::cout << "=== Watershed segmentation ===" << std::endl;

        // Distance transform
        cv::Mat dist_transform;
        cv::distanceTransform(binary, dist_transform, cv::DIST_L2, 5);

        // Dynamic threshold to determine foreground (more aggressive for small particles)
        double min_val, max_val;
        cv::minMaxLoc(dist_transform, &min_val, &max_val);
        double threshold = 0.15 * max_val;  // Even more reduced threshold for better small particle separation

        cv::Mat sure_fg;
        cv::threshold(dist_transform, sure_fg, threshold, 255, cv::THRESH_BINARY);
        sure_fg.convertTo(sure_fg, CV_8U);

        // Determine background (reduced dilation to preserve small particles)
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(2, 2));
        cv::Mat sure_bg;
        cv::dilate(binary, sure_bg, kernel, cv::Point(-1,-1), 1);

        // Unknown region
        cv::Mat unknown;
        cv::subtract(sure_bg, sure_fg, unknown);

        // Connected components labeling
        cv::Mat markers;
        cv::connectedComponents(sure_fg, markers);
        markers = markers + 1;
        markers.setTo(0, unknown);

        // Watershed algorithm
        cv::watershed(src, markers);

        std::cout << "Watershed segmentation completed" << std::endl;
        return markers;
    }

    static std::vector<ParticleInfo> analyzeParticles(const cv::Mat& markers,
                                                     const cv::Mat& src,
                                                     double um_per_pixel) {
        std::cout << "=== Particle analysis started ===" << std::endl;

        std::vector<ParticleInfo> particles;

        double min_val, max_val;
        cv::minMaxLoc(markers, &min_val, &max_val);

        int valid_count = 0;
        int total_count = 0;

        for (int label = 2; label <= static_cast<int>(max_val); ++label) {
            cv::Mat particle_mask = (markers == label);

            std::vector<std::vector<cv::Point>> contours;
            cv::findContours(particle_mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

            if (contours.empty()) continue;

            total_count++;
            std::vector<cv::Point> contour = contours[0];

            // Basic geometric features
            double area = cv::contourArea(contour);
            cv::Moments moments = cv::moments(contour);
            cv::Point2f center(moments.m10/moments.m00, moments.m01/moments.m00);
            cv::Rect bbox = cv::boundingRect(contour);

            // Circularity calculation
            double perimeter = cv::arcLength(contour, true);
            double circularity = (perimeter > 0) ? 4 * CV_PI * area / (perimeter * perimeter) : 0;

            // Equivalent diameter
            double diameter_px = 2 * sqrt(area / CV_PI);
            double diameter_um = (um_per_pixel > 0) ? diameter_px * um_per_pixel : diameter_px;

            // Quality filtering
            bool is_valid = true;
            std::string reject_reason = "";

            // Area filtering (extremely relaxed for maximum particle detection)
            // Allow very small particles down to ~3um diameter
            if (area < 25) {
                is_valid = false;
                reject_reason = "area too small";
            } else if (area > 80000) {
                is_valid = false;
                reject_reason = "area too large";
            }

            // Circularity filtering (very lenient for maximum particle counting)
            if (circularity < 0.2) {
                is_valid = false;
                reject_reason = "insufficient circularity";
            }

            // Boundary filtering (very lenient to maximize detection)
            if (bbox.x <= 0 || bbox.y <= 0 ||
                bbox.x + bbox.width >= src.cols ||
                bbox.y + bbox.height >= src.rows) {
                is_valid = false;
                reject_reason = "touching boundary";
            }

            if (is_valid) {
                valid_count++;
                std::cout << "Particle " << valid_count << ": diameter="
                          << std::fixed << std::setprecision(1) << diameter_um
                          << (um_per_pixel > 0 ? "um" : "px")
                          << ", circularity=" << std::setprecision(3) << circularity << std::endl;
            } else {
                std::cout << "Rejected particle: " << reject_reason
                          << " (diameter=" << std::setprecision(1) << diameter_um
                          << (um_per_pixel > 0 ? "um" : "px") << ")" << std::endl;
            }

            ParticleInfo particle;
            particle.center = center;
            particle.area = area;
            particle.diameter_px = diameter_px;
            particle.diameter_um = diameter_um;
            particle.circularity = circularity;
            particle.is_valid = is_valid;
            particle.bounding_rect = bbox;

            particles.push_back(particle);
        }

        std::cout << "Analysis completed: detected " << total_count << " objects, valid particles "
                  << valid_count << std::endl;

        return particles;
    }

    static cv::Mat visualizeResults(const cv::Mat& src,
                                   const std::vector<ParticleInfo>& particles,
                                   const cv::Mat& markers,
                                   double um_per_pixel) {
        cv::Mat result = src.clone();

        // Draw red contours for all detected particles
        for (int label = 2; label <= 1000; ++label) {  // Reasonable upper limit
            cv::Mat particle_mask = (markers == label);

            std::vector<std::vector<cv::Point>> contours;
            cv::findContours(particle_mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

            if (!contours.empty()) {
                // Draw red contour for each particle
                cv::drawContours(result, contours, -1, cv::Scalar(0, 0, 255), 2);
            }
        }

        // Draw centers and labels for valid particles only
        int valid_id = 1;
        for (const auto& particle : particles) {
            if (particle.is_valid) {
                // Draw center point in green
                cv::circle(result, particle.center, 2, cv::Scalar(0, 255, 0), -1);

                // Add particle ID label
                std::stringstream ss;
                ss << valid_id;

                cv::putText(result, ss.str(),
                           cv::Point(particle.center.x + 5, particle.center.y - 5),
                           cv::FONT_HERSHEY_SIMPLEX, 0.3, cv::Scalar(0, 255, 0), 1);
                valid_id++;
            }
        }

        return result;
    }

    static void printStatistics(const std::vector<ParticleInfo>& particles, double um_per_pixel) {
        std::vector<double> valid_diameters;
        for (const auto& particle : particles) {
            if (particle.is_valid) {
                valid_diameters.push_back(particle.diameter_um);
            }
        }
        
        if (valid_diameters.empty()) {
            std::cout << "\n=== Statistical Results ===" << std::endl;
            std::cout << "No valid particles detected" << std::endl;
            return;
        }

        std::sort(valid_diameters.begin(), valid_diameters.end());

        double sum = 0;
        for (double d : valid_diameters) sum += d;
        double mean = sum / valid_diameters.size();

        double median = (valid_diameters.size() % 2 == 0) ?
            (valid_diameters[valid_diameters.size()/2-1] + valid_diameters[valid_diameters.size()/2]) / 2 :
            valid_diameters[valid_diameters.size()/2];

        std::cout << "\n=== Particle Statistical Results ===" << std::endl;
        std::cout << "Number of valid particles: " << valid_diameters.size() << std::endl;
        std::cout << "Diameter range: " << std::fixed << std::setprecision(1)
                  << valid_diameters.front() << " - " << valid_diameters.back()
                  << (um_per_pixel > 0 ? " um" : " px") << std::endl;
        std::cout << "Average diameter: " << std::setprecision(1) << mean
                  << (um_per_pixel > 0 ? " um" : " px") << std::endl;
        std::cout << "Median diameter: " << std::setprecision(1) << median
                  << (um_per_pixel > 0 ? " um" : " px") << std::endl;
    }
};

int main(int argc, char* argv[]) {
    ArgumentParser parser("particle_segment", "particle_segment tool - segment particles in images");
    parser.addArgument("-i", "--input", "Input image path", true);
    parser.addArgument("-o", "--output", "Output result image path", false, "particle_result.jpg");
    parser.addArgument("-s", "--scale", "Scale bar real length (um)", false, "100");
    parser.addArgument("-v", "--verbose", "Verbose output", false, "false");

    if (!parser.parse(argc, argv)) {
        return -1;
    }

    std::string input_path = parser.getValue("-i");
    std::string output_path = parser.getValue("-o");
    double scale_length = std::stod(parser.getValue("-s"));
    bool verbose = (parser.getValue("-v") == "true");

    // Load image
    cv::Mat src = ImageUtils::loadImage(input_path);
    if (src.empty()) {
        std::cerr << "Error: Cannot read image " << input_path << std::endl;
        return -1;
    }

    std::cout << "Particle segmentation analysis started..." << std::endl;
    std::cout << "Input image: " << input_path << std::endl;
    std::cout << "Image size: " << src.cols << "x" << src.rows << std::endl;
    
    // Scale bar detection
    double um_per_pixel = ParticleSegmentation::detectScaleBar(src, scale_length);

    // Image preprocessing
    cv::Mat preprocessed = ParticleSegmentation::preprocessImage(src);
    if (verbose) {
        std::string preprocess_path = "preprocessed_" + output_path;
        ImageUtils::saveImage(preprocessed, preprocess_path);
        std::cout << "Preprocessing result saved to: " << preprocess_path << std::endl;
    }

    // Binarization
    cv::Mat binary = ParticleSegmentation::createBinaryMask(preprocessed);
    if (verbose) {
        std::string binary_path = "binary_" + output_path;
        ImageUtils::saveImage(binary, binary_path);
        std::cout << "Binary result saved to: " << binary_path << std::endl;
    }

    // Watershed segmentation
    cv::Mat markers = ParticleSegmentation::performWatershed(src, binary);

    // Particle analysis
    std::vector<ParticleSegmentation::ParticleInfo> particles =
        ParticleSegmentation::analyzeParticles(markers, src, um_per_pixel);

    // Result visualization
    cv::Mat result = ParticleSegmentation::visualizeResults(src, particles, markers, um_per_pixel);

    // Statistical information
    ParticleSegmentation::printStatistics(particles, um_per_pixel);

    // Count valid particles
    int valid_particle_count = 0;
    for (const auto& particle : particles) {
        if (particle.is_valid) {
            valid_particle_count++;
        }
    }
    std::cout << "\nTotal segmented particles: " << valid_particle_count << std::endl;

    // Save result
    if (ImageUtils::saveImage(result, output_path)) {
        std::cout << "Result saved to: " << output_path << std::endl;
    }
    
    return 0;
}

