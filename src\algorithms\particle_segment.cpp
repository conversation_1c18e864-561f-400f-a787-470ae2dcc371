#include "ImageUtils.h"
#include "ArgumentParser.h"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>

class ParticleSegmenter {
public:
    static cv::Mat segmentParticles(const cv::Mat& input, const std::string& outputDir, bool showDebug = false);
    static void displayInfo();

private:
    static cv::Mat preprocessImage(const cv::Mat& input, const std::string& outputDir, bool showDebug);
    static std::vector<std::vector<cv::Point>> detectParticles(const cv::Mat& binary, const std::string& outputDir, bool showDebug);
    static cv::Mat drawResults(const cv::Mat& original, const std::vector<std::vector<cv::Point>>& contours, 
                              const std::string& outputDir, int particleCount);
};

cv::Mat ParticleSegmenter::preprocessImage(const cv::Mat& input, const std::string& outputDir, bool showDebug) {
    std::cout << "=== Image Preprocessing ===" << std::endl;
    
    cv::Mat gray, blurred, binary;
    
    // Convert to grayscale
    cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY);
    if (showDebug) {
        ImageUtils::saveImage(gray, outputDir + "/01_grayscale.jpg");
        std::cout << "Saved: 01_grayscale.jpg" << std::endl;
    }
    
    // Apply Gaussian blur to reduce noise
    cv::GaussianBlur(gray, blurred, cv::Size(5, 5), 1.5);
    if (showDebug) {
        ImageUtils::saveImage(blurred, outputDir + "/02_blurred.jpg");
        std::cout << "Saved: 02_blurred.jpg" << std::endl;
    }
    
    // Apply adaptive threshold for better segmentation
    cv::adaptiveThreshold(blurred, binary, 255, cv::ADAPTIVE_THRESH_GAUSSIAN_C, 
                         cv::THRESH_BINARY_INV, 15, 8);
    
    if (showDebug) {
        ImageUtils::saveImage(binary, outputDir + "/03_adaptive_threshold.jpg");
        std::cout << "Saved: 03_adaptive_threshold.jpg" << std::endl;
    }
    
    // Morphological operations to clean up
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));
    cv::Mat cleaned;
    
    // Opening to remove small noise
    cv::morphologyEx(binary, cleaned, cv::MORPH_OPEN, kernel, cv::Point(-1, -1), 1);
    
    // Closing to fill small gaps
    cv::morphologyEx(cleaned, cleaned, cv::MORPH_CLOSE, kernel, cv::Point(-1, -1), 2);
    
    if (showDebug) {
        ImageUtils::saveImage(cleaned, outputDir + "/04_morphology_cleaned.jpg");
        std::cout << "Saved: 04_morphology_cleaned.jpg" << std::endl;
    }
    
    return cleaned;
}

std::vector<std::vector<cv::Point>> ParticleSegmenter::detectParticles(const cv::Mat& binary, 
                                                                        const std::string& outputDir, 
                                                                        bool showDebug) {
    std::cout << "=== Particle Detection ===" << std::endl;
    
    // Find contours
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(binary, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    
    std::cout << "Found " << contours.size() << " initial contours" << std::endl;
    
    // Filter contours based on area and shape
    std::vector<std::vector<cv::Point>> validParticles;
    const double MIN_AREA = 50.0;    // Minimum particle area
    const double MAX_AREA = 10000.0; // Maximum particle area
    const double MIN_CIRCULARITY = 0.3; // Minimum circularity for particle-like objects
    
    for (const auto& contour : contours) {
        double area = cv::contourArea(contour);
        
        // Area filter
        if (area < MIN_AREA || area > MAX_AREA) {
            continue;
        }
        
        // Calculate perimeter and circularity
        double perimeter = cv::arcLength(contour, true);
        double circularity = 4.0 * CV_PI * area / (perimeter * perimeter);
        
        // Circularity filter (particles should be roughly circular)
        if (circularity < MIN_CIRCULARITY) {
            continue;
        }
        
        // Calculate bounding rectangle aspect ratio
        cv::Rect boundingRect = cv::boundingRect(contour);
        double aspectRatio = static_cast<double>(boundingRect.width) / boundingRect.height;
        
        // Aspect ratio filter (avoid very elongated objects)
        if (aspectRatio > 3.0 || aspectRatio < 0.33) {
            continue;
        }
        
        validParticles.push_back(contour);
        
        if (showDebug) {
            std::cout << "Valid particle - Area: " << std::fixed << std::setprecision(1) << area 
                      << ", Circularity: " << std::setprecision(3) << circularity
                      << ", Aspect Ratio: " << std::setprecision(2) << aspectRatio << std::endl;
        }
    }
    
    std::cout << "Filtered to " << validParticles.size() << " valid particles" << std::endl;
    
    // Create debug image showing all detected contours
    if (showDebug) {
        cv::Mat debugContours = cv::Mat::zeros(binary.size(), CV_8UC3);
        for (size_t i = 0; i < validParticles.size(); i++) {
            cv::Scalar color = cv::Scalar(0, 255, 0); // Green for valid particles
            cv::drawContours(debugContours, validParticles, static_cast<int>(i), color, 2);
        }
        ImageUtils::saveImage(debugContours, outputDir + "/05_detected_particles.jpg");
        std::cout << "Saved: 05_detected_particles.jpg" << std::endl;
    }
    
    return validParticles;
}

cv::Mat ParticleSegmenter::drawResults(const cv::Mat& original, 
                                      const std::vector<std::vector<cv::Point>>& contours,
                                      const std::string& /* outputDir */, 
                                      int particleCount) {
    std::cout << "=== Drawing Results ===" << std::endl;
    
    cv::Mat result = original.clone();
    
    // Draw red contours for all detected particles
    for (size_t i = 0; i < contours.size(); i++) {
        cv::drawContours(result, contours, static_cast<int>(i), cv::Scalar(0, 0, 255), 2);
        
        // Add particle number
        cv::Moments M = cv::moments(contours[i]);
        if (M.m00 > 0) {
            cv::Point centroid(static_cast<int>(M.m10 / M.m00), static_cast<int>(M.m01 / M.m00));
            cv::putText(result, std::to_string(i + 1), centroid, 
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 0), 1);
        }
    }
    
    // Add summary text
    std::string countText = "Particles Detected: " + std::to_string(particleCount);
    cv::putText(result, countText, cv::Point(10, 30), 
               cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 0), 2);
    
    std::cout << "Drew " << particleCount << " particle contours in red" << std::endl;
    
    return result;
}

cv::Mat ParticleSegmenter::segmentParticles(const cv::Mat& input, const std::string& outputDir, bool showDebug) {
    std::cout << "\n=== Starting Particle Segmentation ===" << std::endl;
    std::cout << "Input image size: " << input.cols << "x" << input.rows << std::endl;
    
    // Create output directory if it doesn't exist
    ImageUtils::createOutputDirectory(outputDir + "/dummy.jpg");
    
    // Step 1: Preprocess the image
    cv::Mat binary = preprocessImage(input, outputDir, showDebug);
    
    // Step 2: Detect particles
    std::vector<std::vector<cv::Point>> particles = detectParticles(binary, outputDir, showDebug);
    
    // Step 3: Draw results with red contours
    cv::Mat result = drawResults(input, particles, outputDir, static_cast<int>(particles.size()));
    
    std::cout << "\n=== Segmentation Complete ===" << std::endl;
    std::cout << "Total particles detected: " << particles.size() << std::endl;
    
    return result;
}

void ParticleSegmenter::displayInfo() {
    std::cout << "\nParticle Segmentation Tool" << std::endl;
    std::cout << "=========================" << std::endl;
    std::cout << "This tool segments and counts particles in microscope images." << std::endl;
    std::cout << "Features:" << std::endl;
    std::cout << "- Adaptive thresholding for robust segmentation" << std::endl;
    std::cout << "- Morphological filtering to remove noise" << std::endl;
    std::cout << "- Shape-based filtering (area, circularity, aspect ratio)" << std::endl;
    std::cout << "- Red contour marking for detected particles" << std::endl;
    std::cout << "- Debug output showing intermediate processing steps" << std::endl;
    std::cout << "- Particle counting with numbered labels" << std::endl;
}

int main(int argc, char* argv[]) {
    ArgumentParser parser("particle_segment", "Particle Segmentation and Counting Tool");
    parser.addArgument("-i", "--input", "Input image path", true);
    parser.addArgument("-o", "--output", "Output directory path", true);
    parser.addArgument("-d", "--debug", "Show debug images (0/1)", false);
    parser.addArgument("-h", "--help", "Show help information", false, false);
    
    if (!parser.parse(argc, argv)) {
        parser.showHelp();
        return -1;
    }
    
    if (parser.hasArgument("--help")) {
        ParticleSegmenter::displayInfo();
        parser.showHelp();
        return 0;
    }
    
    std::string inputPath = parser.getValue("--input");
    std::string outputDir = parser.getValue("--output");
    std::string debugValue = parser.getValue("--debug");
    bool showDebug = (debugValue == "1" || debugValue == "true");
    
    // Load input image
    cv::Mat input = ImageUtils::loadImage(inputPath);
    if (input.empty()) {
        std::cerr << "Error: Could not load image from " << inputPath << std::endl;
        return -1;
    }
    
    // Process the image
    cv::Mat result = ParticleSegmenter::segmentParticles(input, outputDir, showDebug);
    
    // Save final result
    std::string outputPath = outputDir + "/particle_segmentation_result.jpg";
    if (ImageUtils::saveImage(result, outputPath)) {
        std::cout << "Result saved to: " << outputPath << std::endl;
    } else {
        std::cerr << "Error: Could not save result image" << std::endl;
        return -1;
    }
    
    // Display result
    cv::imshow("Particle Segmentation Result", result);
    cv::waitKey(0);
    cv::destroyAllWindows();
    
    return 0;
}