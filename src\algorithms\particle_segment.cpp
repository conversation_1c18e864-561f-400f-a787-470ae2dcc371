#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <sstream>
#include "ImageUtils.h"
#include "ArgumentParser.h"

class ParticleSegmentation {
public:
    struct ParticleInfo {
        cv::Point2f center;
        double area;
        double diameter_px;
        double diameter_um;
        double circularity;
        bool is_valid;
        cv::Rect bounding_rect;
    };

    static double detectScaleBar(const cv::Mat& image, double scale_bar_real_length_um) {
        std::cout << "=== 标尺检测开始 ===" << std::endl;
        
        cv::Mat gray, binary;
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY_INV | cv::THRESH_OTSU);
        
        // 水平形态学开运算检测标尺
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(50, 3));
        cv::Mat scale_candidates;
        cv::morphologyEx(binary, scale_candidates, cv::MORPH_OPEN, kernel);
        
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(scale_candidates, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        
        double max_width = 0;
        cv::Rect scale_rect;
        
        for (const auto& contour : contours) {
            cv::Rect rect = cv::boundingRect(contour);
            double aspect_ratio = static_cast<double>(rect.width) / rect.height;
            
            // 标尺特征：长宽比大，位置在图像下方
            if (aspect_ratio > 10 && rect.width > 80 && rect.y > image.rows * 0.7) {
                if (rect.width > max_width) {
                    max_width = rect.width;
                    scale_rect = rect;
                }
            }
        }
        
        if (max_width > 0) {
            double um_per_pixel = scale_bar_real_length_um / max_width;
            std::cout << "标尺检测成功: " << max_width << " 像素 = " 
                      << scale_bar_real_length_um << " μm" << std::endl;
            std::cout << "转换系数: " << std::fixed << std::setprecision(4) 
                      << um_per_pixel << " μm/像素" << std::endl;
            return um_per_pixel;
        }
        
        std::cout << "警告: 未检测到标尺，将使用像素单位" << std::endl;
        return 0.0;
    }

    static cv::Mat preprocessImage(const cv::Mat& src) {
        std::cout << "=== 图像预处理开始 ===" << std::endl;
        
        cv::Mat gray, enhanced, blurred;
        cv::cvtColor(src, gray, cv::COLOR_BGR2GRAY);
        
        // CLAHE对比度增强
        cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(2.0, cv::Size(8, 8));
        clahe->apply(gray, enhanced);
        
        // 轻微高斯模糊平滑内部纹理
        cv::GaussianBlur(enhanced, blurred, cv::Size(5, 5), 1.0);
        
        std::cout << "预处理完成: CLAHE增强 + 高斯模糊" << std::endl;
        return blurred;
    }

    static cv::Mat createBinaryMask(const cv::Mat& preprocessed) {
        std::cout << "=== 二值化处理 ===" << std::endl;
        
        cv::Mat binary, cleaned;
        
        // Otsu自动阈值
        cv::threshold(preprocessed, binary, 0, 255, cv::THRESH_BINARY_INV | cv::THRESH_OTSU);
        
        // 形态学操作清理
        cv::Mat kernel_open = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));
        cv::Mat kernel_close = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(7, 7));
        
        cv::morphologyEx(binary, cleaned, cv::MORPH_OPEN, kernel_open);
        cv::morphologyEx(cleaned, cleaned, cv::MORPH_CLOSE, kernel_close, cv::Point(-1,-1), 2);
        
        std::cout << "二值化完成: Otsu阈值 + 形态学清理" << std::endl;
        return cleaned;
    }

    static cv::Mat performWatershed(const cv::Mat& src, const cv::Mat& binary) {
        std::cout << "=== Watershed分割 ===" << std::endl;
        
        // 距离变换
        cv::Mat dist_transform;
        cv::distanceTransform(binary, dist_transform, cv::DIST_L2, 5);
        
        // 动态阈值确定前景
        double min_val, max_val;
        cv::minMaxLoc(dist_transform, &min_val, &max_val);
        double threshold = 0.5 * max_val;  // 可调参数
        
        cv::Mat sure_fg;
        cv::threshold(dist_transform, sure_fg, threshold, 255, cv::THRESH_BINARY);
        sure_fg.convertTo(sure_fg, CV_8U);
        
        // 确定背景
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(5, 5));
        cv::Mat sure_bg;
        cv::dilate(binary, sure_bg, kernel, cv::Point(-1,-1), 3);
        
        // 未知区域
        cv::Mat unknown;
        cv::subtract(sure_bg, sure_fg, unknown);
        
        // 连通组件标记
        cv::Mat markers;
        cv::connectedComponents(sure_fg, markers);
        markers = markers + 1;
        markers.setTo(0, unknown);
        
        // Watershed算法
        cv::watershed(src, markers);
        
        std::cout << "Watershed分割完成" << std::endl;
        return markers;
    }

    static std::vector<ParticleInfo> analyzeParticles(const cv::Mat& markers, 
                                                     const cv::Mat& src,
                                                     double um_per_pixel) {
        std::cout << "=== 颗粒分析开始 ===" << std::endl;
        
        std::vector<ParticleInfo> particles;
        
        double min_val, max_val;
        cv::minMaxLoc(markers, &min_val, &max_val);
        
        int valid_count = 0;
        int total_count = 0;
        
        for (int label = 2; label <= static_cast<int>(max_val); ++label) {
            cv::Mat particle_mask = (markers == label);
            
            std::vector<std::vector<cv::Point>> contours;
            cv::findContours(particle_mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
            
            if (contours.empty()) continue;
            
            total_count++;
            std::vector<cv::Point> contour = contours[0];
            
            // 基本几何特征
            double area = cv::contourArea(contour);
            cv::Moments moments = cv::moments(contour);
            cv::Point2f center(moments.m10/moments.m00, moments.m01/moments.m00);
            cv::Rect bbox = cv::boundingRect(contour);
            
            // 圆形度计算
            double perimeter = cv::arcLength(contour, true);
            double circularity = (perimeter > 0) ? 4 * CV_PI * area / (perimeter * perimeter) : 0;
            
            // 等效直径
            double diameter_px = 2 * sqrt(area / CV_PI);
            double diameter_um = (um_per_pixel > 0) ? diameter_px * um_per_pixel : diameter_px;
            
            // 质量过滤
            bool is_valid = true;
            std::string reject_reason = "";
            
            // 面积过滤 (40-120μm直径对应的像素面积范围)
            if (area < 500) {
                is_valid = false;
                reject_reason = "面积过小";
            } else if (area > 50000) {
                is_valid = false;
                reject_reason = "面积过大";
            }
            
            // 圆形度过滤
            if (circularity < 0.6) {
                is_valid = false;
                reject_reason = "圆形度不足";
            }
            
            // 边界过滤
            if (bbox.x <= 2 || bbox.y <= 2 || 
                bbox.x + bbox.width >= src.cols - 2 || 
                bbox.y + bbox.height >= src.rows - 2) {
                is_valid = false;
                reject_reason = "接触边界";
            }
            
            if (is_valid) {
                valid_count++;
                std::cout << "颗粒 " << valid_count << ": 直径=" 
                          << std::fixed << std::setprecision(1) << diameter_um 
                          << (um_per_pixel > 0 ? "μm" : "px") 
                          << ", 圆形度=" << std::setprecision(3) << circularity << std::endl;
            } else {
                std::cout << "拒绝颗粒: " << reject_reason 
                          << " (直径=" << std::setprecision(1) << diameter_um 
                          << (um_per_pixel > 0 ? "μm" : "px") << ")" << std::endl;
            }
            
            ParticleInfo particle;
            particle.center = center;
            particle.area = area;
            particle.diameter_px = diameter_px;
            particle.diameter_um = diameter_um;
            particle.circularity = circularity;
            particle.is_valid = is_valid;
            particle.bounding_rect = bbox;
            
            particles.push_back(particle);
        }
        
        std::cout << "分析完成: 检测到 " << total_count << " 个对象, 有效颗粒 " 
                  << valid_count << " 个" << std::endl;
        
        return particles;
    }

    static cv::Mat visualizeResults(const cv::Mat& src, 
                                   const std::vector<ParticleInfo>& particles,
                                   const cv::Mat& markers,
                                   double um_per_pixel) {
        cv::Mat result = src.clone();
        
        // 绘制watershed边界
        for (int i = 0; i < markers.rows; i++) {
            for (int j = 0; j < markers.cols; j++) {
                if (markers.at<int>(i, j) == -1) {
                    result.at<cv::Vec3b>(i, j) = cv::Vec3b(0, 255, 255);
                }
            }
        }
        
        int valid_id = 1;
        for (const auto& particle : particles) {
            if (particle.is_valid) {
                // 绘制有效颗粒
                cv::circle(result, particle.center, 3, cv::Scalar(0, 255, 0), -1);
                cv::circle(result, particle.center, particle.diameter_px/2, cv::Scalar(0, 255, 0), 2);
                
                // 标注ID和尺寸
                std::stringstream ss;
                ss << valid_id << ": " << std::fixed << std::setprecision(1) 
                   << particle.diameter_um << (um_per_pixel > 0 ? "μm" : "px");
                
                cv::putText(result, ss.str(), 
                           cv::Point(particle.center.x - 20, particle.center.y - 15),
                           cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 255, 0), 1);
                valid_id++;
            } else {
                // 绘制无效颗粒
                cv::circle(result, particle.center, 3, cv::Scalar(0, 0, 255), -1);
                cv::circle(result, particle.center, particle.diameter_px/2, cv::Scalar(0, 0, 255), 1);
            }
        }
        
        return result;
    }

    static void printStatistics(const std::vector<ParticleInfo>& particles, double um_per_pixel) {
        std::vector<double> valid_diameters;
        for (const auto& particle : particles) {
            if (particle.is_valid) {
                valid_diameters.push_back(particle.diameter_um);
            }
        }
        
        if (valid_diameters.empty()) {
            std::cout << "\n=== 统计结果 ===" << std::endl;
            std::cout << "未检测到有效颗粒" << std::endl;
            return;
        }
        
        std::sort(valid_diameters.begin(), valid_diameters.end());
        
        double sum = 0;
        for (double d : valid_diameters) sum += d;
        double mean = sum / valid_diameters.size();
        
        double median = (valid_diameters.size() % 2 == 0) ?
            (valid_diameters[valid_diameters.size()/2-1] + valid_diameters[valid_diameters.size()/2]) / 2 :
            valid_diameters[valid_diameters.size()/2];
        
        std::cout << "\n=== 颗粒统计结果 ===" << std::endl;
        std::cout << "有效颗粒数量: " << valid_diameters.size() << std::endl;
        std::cout << "直径范围: " << std::fixed << std::setprecision(1) 
                  << valid_diameters.front() << " - " << valid_diameters.back() 
                  << (um_per_pixel > 0 ? " μm" : " px") << std::endl;
        std::cout << "平均直径: " << std::setprecision(1) << mean 
                  << (um_per_pixel > 0 ? " μm" : " px") << std::endl;
        std::cout << "中位直径: " << std::setprecision(1) << median 
                  << (um_per_pixel > 0 ? " μm" : " px") << std::endl;
    }
};

int main(int argc, char* argv[]) {
    ArgumentParser parser("particle_segment", "particle_segment tool - segment particles in images");
    parser.addArgument("-i", "--input", "输入图像路径", true);
    parser.addArgument("-o", "--output", "输出结果图像路径", false, "particle_result.jpg");
    parser.addArgument("-s", "--scale", "标尺实际长度(μm)", false, "100");
    parser.addArgument("-v", "--verbose", "详细输出", false, "false");
    
    if (!parser.parse(argc, argv)) {
        return -1;
    }
    
    std::string input_path = parser.getValue("-i");
    std::string output_path = parser.getValue("-o");
    double scale_length = std::stod(parser.getValue("-s"));
    bool verbose = (parser.getValue("-v") == "true");
    
    // 读取图像
    cv::Mat src = ImageUtils::loadImage(input_path);
    if (src.empty()) {
        std::cerr << "错误: 无法读取图像 " << input_path << std::endl;
        return -1;
    }
    
    std::cout << "颗粒分割分析开始..." << std::endl;
    std::cout << "输入图像: " << input_path << std::endl;
    std::cout << "图像尺寸: " << src.cols << "x" << src.rows << std::endl;
    
    // 标尺检测
    double um_per_pixel = ParticleSegmentation::detectScaleBar(src, scale_length);
    
    // 图像预处理
    cv::Mat preprocessed = ParticleSegmentation::preprocessImage(src);
    if (verbose) cv::imshow("预处理结果", preprocessed);
    
    // 二值化
    cv::Mat binary = ParticleSegmentation::createBinaryMask(preprocessed);
    if (verbose) cv::imshow("二值化结果", binary);
    
    // Watershed分割
    cv::Mat markers = ParticleSegmentation::performWatershed(src, binary);
    
    // 颗粒分析
    std::vector<ParticleSegmentation::ParticleInfo> particles = 
        ParticleSegmentation::analyzeParticles(markers, src, um_per_pixel);
    
    // 结果可视化
    cv::Mat result = ParticleSegmentation::visualizeResults(src, particles, markers, um_per_pixel);
    
    // 统计信息
    ParticleSegmentation::printStatistics(particles, um_per_pixel);
    
    // 保存结果
    if (ImageUtils::saveImage(result, output_path)) {
        std::cout << "\n结果已保存到: " << output_path << std::endl;
    }
    
    // 显示结果
    cv::imshow("颗粒分割结果", result);
    cv::waitKey(0);
    cv::destroyAllWindows();
    
    return 0;
}

