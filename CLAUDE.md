# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

### Build Project
```bash
# Create build directory and compile
mkdir build && cd build && cmake .. -G "MinGW Makefiles" && mingw32-make -j4

# Quick rebuild (from build directory)
mingw32-make -j4

# Clean and rebuild
mingw32-make clean && mingw32-make -j4
```

### Runtime Environment
Before running executables, set OpenCV DLL path:
```bash
# In Git Bash
export PATH="/d/00-infinuo/install/x64/mingw/bin:$PATH"

# In Windows CMD
set PATH=D:\00-infinuo\install\x64\mingw\bin;%PATH%
```

## Architecture Overview

This is a modular C++ OpenCV image processing framework where **each algorithm is compiled as an independent executable**.

### Core Components
- **ImageUtils**: Static utility class for image I/O, validation, and directory creation
- **ArgumentParser**: Unified command-line argument parsing for all algorithms
- **Algorithm executables**: Independent programs in `build/bin/` directory

### Project Structure
```
src/
├── utils/              # Shared utility implementations
│   ├── ImageUtils.cpp      # Image operations and file handling
│   └── ArgumentParser.cpp  # CLI parsing with help generation
└── algorithms/         # Independent algorithm implementations
    ├── grayscale_converter.cpp
    ├── edge_detector.cpp
    ├── image_blur.cpp
    ├── particle_analysis.cpp
    └── particle_segment.cpp
```

### Architecture Patterns
1. **Static Utility Classes**: ImageUtils and ArgumentParser provide shared functionality
2. **Independent Executables**: Each algorithm has its own main() function and executable
3. **Consistent CLI Interface**: All programs use ArgumentParser for unified argument handling
4. **Modular Design**: Add new algorithms by creating new .cpp files and updating CMakeLists.txt

## Development Workflow

### Adding New Algorithms
1. Create new `.cpp` file in `src/algorithms/`
2. Include required headers: `ImageUtils.h`, `ArgumentParser.h`, `<opencv2/opencv.hpp>`
3. Implement algorithm class with static methods
4. Add main() function using ArgumentParser
5. Update `CMakeLists.txt` ALGORITHMS list:
```cmake
set(ALGORITHMS
    grayscale_converter
    edge_detector
    image_blur
    particle_analysis
    particle_segment
    your_new_algorithm  # Add here
)
```

### OpenCV Configuration
- **Installation Path**: D:/00-infinuo/install
- **Version**: 4.12.0
- **Compiler**: MinGW64 GCC with C++17 standard
- **Linking**: Static linking configured for portability

### Common Algorithm Structure
```cpp
#include "ImageUtils.h"
#include "ArgumentParser.h"
#include <opencv2/opencv.hpp>

class YourAlgorithm {
public:
    static cv::Mat process(const cv::Mat& input, /* parameters */);
    static void displayInfo();
};

int main(int argc, char* argv[]) {
    ArgumentParser parser;
    // Add arguments
    parser.parse(argc, argv);
    
    cv::Mat input = ImageUtils::loadImage(inputPath);
    cv::Mat result = YourAlgorithm::process(input, params);
    ImageUtils::saveImage(result, outputPath);
}
```